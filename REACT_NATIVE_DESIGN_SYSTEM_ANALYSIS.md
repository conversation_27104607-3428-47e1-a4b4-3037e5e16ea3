# React Native Design System Analysis for Flutter Migration

## Overview
Complete design system extracted from the React Native CultureConnect version for pixel-perfect Flutter implementation.

## 🎨 Color Palette

### Primary Colors (Airbnb Style)
```dart
// Primary brand colors - Airbnb signature red/pink
static const Color primaryColor = Color(0xFFFF385C);
static const Color primaryLight = Color(0xFFFF5A75);
static const Color primaryDark = Color(0xFFE8294A);
```

### Secondary Colors
```dart
static const Color secondaryColor = Color(0xFF00A699); // Teal
static const Color accentColor = Color(0xFFFC642D); // Orange
```

### Neutral Colors
```dart
static const Color backgroundColor = Color(0xFFFFFFFF); // White
static const Color backgroundSecondary = Color(0xFFF7F7F7);
static const Color backgroundTertiary = Color(0xFFF0F0F0);
```

### Text Colors
```dart
static const Color textColor = Color(0xFF222222); // Dark gray
static const Color textSecondary = Color(0xFF717171); // Gray
static const Color textLight = Color(0xFFB0B0B0); // Light gray
static const Color textInverse = Color(0xFFFFFFFF);
```

### UI Colors
```dart
static const Color borderColor = Color(0xFFDDDDDD); // Light gray border
static const Color borderLight = Color(0xFFEBEBEB);
static const Color white = Color(0xFFFFFFFF);
static const Color black = Color(0xFF000000);
```

### Status Colors
```dart
static const Color successColor = Color(0xFF00A699);
static const Color errorColor = Color(0xFFC13515);
static const Color warningColor = Color(0xFFFC642D);
static const Color infoColor = Color(0xFF00A699);
```

### Overlay & Shadow Colors
```dart
static const Color overlayColor = Color(0x80222222); // rgba(34, 34, 34, 0.5)
static const Color overlayLight = Color(0x4D222222); // rgba(34, 34, 34, 0.3)
static const Color shadowColor = Color(0x1F222222); // rgba(34, 34, 34, 0.12)
static const Color shadowDark = Color(0x40222222); // rgba(34, 34, 34, 0.25)
```

## 📏 Spacing System
```dart
static const double spacingXs = 4.0;
static const double spacingSm = 8.0;
static const double spacingMd = 16.0;
static const double spacingLg = 24.0;
static const double spacingXl = 32.0;
static const double spacingXxl = 48.0;
```

## 🔤 Typography System
```dart
// Font Family
static const String fontFamily = 'System'; // Use default system font

// Font Sizes
static const double fontSizeXs = 12.0;
static const double fontSizeSm = 14.0;
static const double fontSizeMd = 16.0;
static const double fontSizeLg = 18.0;
static const double fontSizeXl = 20.0;
static const double fontSizeXxl = 28.0;

// Font Weights
static const FontWeight fontWeightLight = FontWeight.w300;
static const FontWeight fontWeightRegular = FontWeight.w400;
static const FontWeight fontWeightMedium = FontWeight.w500;
static const FontWeight fontWeightSemibold = FontWeight.w600;
static const FontWeight fontWeightBold = FontWeight.w700;

// Line Heights
static const double lineHeightTight = 1.2;
static const double lineHeightNormal = 1.4;
static const double lineHeightRelaxed = 1.6;
```

## 🎯 Border Radius System
```dart
static const double borderRadiusSmall = 8.0;
static const double borderRadiusMedium = 12.0;
static const double borderRadiusLarge = 16.0;
static const double borderRadiusXLarge = 20.0;
```

## 🌟 Shadow System
```dart
// Light Shadow (elevation 2)
static const List<BoxShadow> shadowLight = [
  BoxShadow(
    color: Color(0x1A222222), // shadowColor with 0.1 opacity
    offset: Offset(0, 2),
    blurRadius: 4,
    spreadRadius: 0,
  ),
];

// Medium Shadow (elevation 4)
static const List<BoxShadow> shadowMedium = [
  BoxShadow(
    color: Color(0x26222222), // shadowColor with 0.15 opacity
    offset: Offset(0, 4),
    blurRadius: 8,
    spreadRadius: 0,
  ),
];

// Heavy Shadow (elevation 8)
static const List<BoxShadow> shadowHeavy = [
  BoxShadow(
    color: Color(0x26222222), // shadowColor with 0.15 opacity
    offset: Offset(0, 8),
    blurRadius: 16,
    spreadRadius: 0,
  ),
];
```

## 🎨 Component Patterns

### Button Styles
- **Primary Button**: Dark background (#1a1a2e), white text, 12px border radius
- **Secondary Button**: Teal background, white text
- **Outline Button**: Transparent background, primary border, primary text
- **Padding**: Small (4px/16px), Medium (8px/24px), Large (16px/24px)

### Card Styles
- **Border Radius**: 20px for destination cards, 16px for action buttons
- **Shadow**: Heavy shadow for cards (0, 8, 16, 0.15 opacity)
- **Background**: White with gradient overlays for image cards

### Search Bar
- **Background**: White
- **Border Radius**: 12px
- **Padding**: 16px horizontal, 8px vertical
- **Shadow**: Light shadow (0, 2, 4, 0.1 opacity)

### Navigation Bar
- **Height**: iOS: 88px, Android: 72px
- **Background**: White
- **Border**: Top border with light color
- **Shadow**: Upward shadow (0, -2, 8, 0.1 opacity)
- **Active Color**: Primary red (#FF385C)
- **Inactive Color**: Light gray (#B0B0B0)

## 🎭 Animation Constants
```dart
// Duration constants
static const Duration animationFast = Duration(milliseconds: 150);
static const Duration animationMedium = Duration(milliseconds: 300);
static const Duration animationSlow = Duration(milliseconds: 600);

// Spring animation parameters
static const double springDamping = 15.0;
static const double springStiffness = 300.0;

// Scale animation values
static const double scalePressed = 0.95;
static const double scaleHover = 1.02;
static const double scaleNormal = 1.0;
```

## 🏷️ Tag Color System
```dart
// Dynamic tag colors based on content
static Color getTagColor(String tag) {
  final tagLower = tag.toLowerCase();
  if (tagLower.contains('cultural') || tagLower.contains('culture')) {
    return const Color(0xFF00A699); // Teal
  } else if (tagLower.contains('scenic') || tagLower.contains('nature') || tagLower.contains('beach')) {
    return const Color(0xFFFC642D); // Orange
  } else if (tagLower.contains('historic') || tagLower.contains('history')) {
    return const Color(0xFFFF385C); // Primary red
  } else if (tagLower.contains('adventure')) {
    return const Color(0xFFFC642D); // Orange/Yellow
  } else if (tagLower.contains('city')) {
    return const Color(0xFF8B5CF6); // Purple
  } else if (tagLower.contains('mountain')) {
    return const Color(0xFF10B981); // Green
  } else if (tagLower.contains('food')) {
    return const Color(0xFFF59E0B); // Amber
  } else {
    return const Color(0xFFFF385C); // Default to primary
  }
}
```

## 📱 Layout Specifications

### Quick Actions Grid
- **Button Width**: 80px
- **Spacing**: 16px between buttons
- **Layout**: Horizontal scroll
- **Background Colors**: Themed per action type

### Destination Cards
- **Height**: 280px
- **Aspect Ratio**: Maintain image aspect
- **Gradient Overlay**: Transparent to black (0.7 opacity)
- **Content Positioning**: Top (tags/favorite) and bottom (details)

### Tab Bar Icons
- **Size**: 24px
- **Active State**: Primary color with bold weight
- **Inactive State**: Light gray with medium weight

## 🎯 Implementation Priority
1. **Colors & Typography**: Foundation system
2. **Spacing & Shadows**: Layout consistency
3. **Component Styles**: Buttons, cards, inputs
4. **Animation System**: Micro-interactions
5. **Layout Patterns**: Screen-specific implementations

## 📋 Next Steps
1. Update Flutter app_theme.dart with these specifications
2. Create component-specific style classes
3. Implement animation constants and utilities
4. Test visual parity with React Native version
