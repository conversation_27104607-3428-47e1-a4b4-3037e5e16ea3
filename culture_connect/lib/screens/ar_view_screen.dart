import 'dart:async';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:culture_connect/services/ar_service.dart';
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/services/ar_device_compatibility_service.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/widgets/experience_card.dart';

class ARViewScreen extends StatefulWidget {
  const ARViewScreen({super.key});

  @override
  State<ARViewScreen> createState() => _ARViewScreenState();
}

class _ARViewScreenState extends State<ARViewScreen> {
  ARService? _arService;
  bool _isInitialized = false;
  Experience? _currentExperience;
  bool _isLoading = true;
  String? _errorMessage;

  // Optimized: Timer for AR detection instead of recursive Future.delayed
  Timer? _detectionTimer;

  @override
  void initState() {
    super.initState();
    _initializeAR();
  }

  Future<void> _initializeAR() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Check device compatibility first
      final compatibilityResult =
          await ARDeviceCompatibilityService.checkARCompatibility();

      if (!compatibilityResult.isSupported) {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _isInitialized = false;
            _errorMessage = 'AR not supported: ${compatibilityResult.reason}';
          });
        }
        return;
      }

      _arService = ARService(LocationService());
      await _arService!.initialize();
      await _arService!.startAR();

      if (mounted) {
        setState(() {
          _isInitialized = true;
          _isLoading = false;
        });
        _startExperienceDetection();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isInitialized = false;
          _errorMessage = 'Error initializing AR: $e';
        });
      }
    }
  }

  // Optimized: Timer-based detection instead of recursive Future.delayed
  void _startExperienceDetection() {
    // Cancel existing timer if any
    _detectionTimer?.cancel();

    // Start periodic detection with proper timer
    _detectionTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      _detectExperience();
    });
  }

  Future<void> _detectExperience() async {
    if (!_isInitialized || _arService == null) return;

    try {
      final experience = await _arService!.detectExperienceInView();
      if (mounted && experience != _currentExperience) {
        setState(() {
          _currentExperience = experience;
        });
      }
    } catch (e) {
      // Handle detection errors silently
    }
  }

  // Optimized: Proper cleanup of timer and AR resources
  @override
  void dispose() {
    _detectionTimer?.cancel();
    _arService?.stopAR();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Colors.white),
              SizedBox(height: 16),
              Text(
                'Initializing AR...',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }

    if (!_isInitialized || _arService == null) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.white,
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage ?? 'Failed to initialize AR',
                style: const TextStyle(color: Colors.white, fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _initializeAR,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    final cameraController = _arService?.cameraController;

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera Preview
          if (cameraController != null && cameraController.value.isInitialized)
            SizedBox.expand(
              child: FittedBox(
                fit: BoxFit.cover,
                child: SizedBox(
                  width: cameraController.value.previewSize?.width ?? 1,
                  height: cameraController.value.previewSize?.height ?? 1,
                  child: CameraPreview(cameraController),
                ),
              ),
            )
          else
            // Fallback when camera is not available
            Container(
              color: Colors.black,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.camera_alt_outlined,
                      size: 100,
                      color: Colors.white24,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Camera not available',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),

          // AR Overlay
          if (_currentExperience != null)
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: ExperienceCard(
                title: _currentExperience!.title,
                imageUrl: _currentExperience!.imageUrl,
                rating: _currentExperience!.rating,
                reviewCount: _currentExperience!.reviewCount,
                price: _currentExperience!.price.toString(),
                category: _currentExperience!.category,
                location: _currentExperience!.location,
                onTap: () {
                  // Navigate to experience details
                  Navigator.pushNamed(
                    context,
                    '/experience_details',
                    arguments: _currentExperience!.id,
                  );
                },
              ),
            ),

          // Close Button
          Positioned(
            top: 16,
            right: 16,
            child: SafeArea(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () {
                    if (mounted && Navigator.canPop(context)) {
                      Navigator.pop(context);
                    }
                  },
                ),
              ),
            ),
          ),

          // Instructions
          Positioned(
            top: 16,
            left: 16,
            right: 80,
            child: SafeArea(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  'Point your camera at a location to see information',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
