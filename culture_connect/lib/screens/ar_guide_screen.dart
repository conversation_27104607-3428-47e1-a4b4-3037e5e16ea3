import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/screens/ar_explore_screen.dart';

/// AR Guide Screen - Main hub for AR experiences
/// Matches React Native AR Guide tab functionality
class ARGuideScreen extends ConsumerWidget {
  const ARGuideScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppTheme.spacingMd),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                'AR Guide',
                style: TextStyle(
                  fontSize: AppTheme.fontSizeXxl,
                  fontWeight: AppTheme.fontWeightBold,
                  color: AppTheme.textPrimaryColor,
                  fontFamily: AppTheme.fontFamily,
                ),
              ),
              const SizedBox(height: AppTheme.spacingSm),
              Text(
                'Explore the world through augmented reality',
                style: TextStyle(
                  fontSize: AppTheme.fontSizeMd,
                  color: AppTheme.textSecondaryColor,
                  fontFamily: AppTheme.fontFamily,
                ),
              ),
              const SizedBox(height: AppTheme.spacingLg),

              // Quick AR Actions
              _buildQuickActions(context),

              const SizedBox(height: AppTheme.spacingLg),

              // Featured AR Experiences
              _buildFeaturedExperiences(context),

              const SizedBox(height: AppTheme.spacingLg),

              // AR Tutorial Section
              _buildTutorialSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: AppTheme.shadowLight,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: TextStyle(
              fontSize: AppTheme.fontSizeLg,
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
              fontFamily: AppTheme.fontFamily,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  context,
                  'Start AR',
                  Icons.camera_alt_outlined,
                  () => _startARExperience(context),
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: _buildActionButton(
                  context,
                  'AR Tutorial',
                  Icons.school_outlined,
                  () => _openTutorial(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: AppTheme.spacingSm),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedExperiences(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Featured AR Experiences',
          style: TextStyle(
            fontSize: AppTheme.fontSizeLg,
            fontWeight: AppTheme.fontWeightBold,
            color: AppTheme.textPrimaryColor,
            fontFamily: AppTheme.fontFamily,
          ),
        ),
        const SizedBox(height: AppTheme.spacingMd),
        Container(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 3,
            itemBuilder: (context, index) {
              return Container(
                width: 160,
                margin: EdgeInsets.only(
                  right: index < 2 ? AppTheme.spacingMd : 0,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.surfaceColor,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusMedium),
                  boxShadow: AppTheme.shadowLight,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withAlpha(50),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(AppTheme.borderRadiusMedium),
                          topRight:
                              Radius.circular(AppTheme.borderRadiusMedium),
                        ),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.view_in_ar,
                          size: 48,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(AppTheme.spacingSm),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'AR Experience ${index + 1}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Discover amazing places',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTutorialSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'New to AR?',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          const Text(
            'Learn how to use AR features with our interactive tutorial',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          ElevatedButton(
            onPressed: () => _openTutorial(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
            ),
            child: const Text('Start Tutorial'),
          ),
        ],
      ),
    );
  }

  void _startARExperience(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ARExploreScreen(),
      ),
    );
  }

  void _openTutorial(BuildContext context) {
    // Navigate to AR tutorial screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('AR Tutorial coming soon!'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }
}
