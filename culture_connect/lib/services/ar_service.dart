import 'package:flutter/material.dart' hide Colors;
import 'package:arcore_flutter_plugin/arcore_flutter_plugin.dart';
import 'package:vector_math/vector_math_64.dart';
import 'package:camera/camera.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:culture_connect/models/landmark.dart';
import 'package:culture_connect/models/experience.dart';
import 'location_service.dart';
import 'dart:io' show Platform;

class ARService {
  ArCoreController? _arController;
  CameraController? _cameraController;
  bool _isInitialized = false;
  bool _isARSupported = false;
  final LocationService _locationService;

  ARService(this._locationService);

  // Use the location service to get nearby experiences
  Future<List<Experience>> getNearbyExperiences() async {
    try {
      final currentPosition = await _locationService.getCurrentPosition();
      // In a real app, this would query a backend for experiences near the current location
      // For now, we'll return a mock list
      return [
        Experience(
          id: 'exp1',
          title: 'Cultural Tour of Lagos',
          description:
              'Explore the rich cultural heritage of Lagos with our expert guides.',
          category: 'Cultural',
          price: 50.0,
          location: 'Lagos, Nigeria',
          imageUrl: 'https://example.com/lagos.jpg',
          rating: 4.5,
          reviewCount: 120,
          guideId: 'guide1',
          guideName: 'John Doe',
          guideImageUrl: 'https://example.com/guide.jpg',
          coordinates: LatLng(
            currentPosition.latitude,
            currentPosition.longitude,
          ),
          languages: ['English', 'Yoruba'],
          includedItems: ['Tour guide', 'Water', 'Snacks'],
          requirements: ['Comfortable shoes', 'Sun protection'],
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          updatedAt: DateTime.now().subtract(const Duration(days: 5)),
          durationHours: 2.0,
        ),
      ];
    } catch (e) {
      debugPrint('Error getting nearby experiences: $e');
      // Return a default experience with a fixed location
      return [
        Experience(
          id: 'exp1',
          title: 'Cultural Tour of Lagos',
          description:
              'Explore the rich cultural heritage of Lagos with our expert guides.',
          category: 'Cultural',
          price: 50.0,
          location: 'Lagos, Nigeria',
          imageUrl: 'https://example.com/lagos.jpg',
          rating: 4.5,
          reviewCount: 120,
          guideId: 'guide1',
          guideName: 'John Doe',
          guideImageUrl: 'https://example.com/guide.jpg',
          coordinates: const LatLng(6.5244, 3.3792),
          languages: ['English', 'Yoruba'],
          includedItems: ['Tour guide', 'Water', 'Snacks'],
          requirements: ['Comfortable shoes', 'Sun protection'],
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          updatedAt: DateTime.now().subtract(const Duration(days: 5)),
          durationHours: 2.0,
        ),
      ];
    }
  }

  CameraController? get cameraController => _cameraController;
  bool get isARSupported => _isARSupported;
  bool get isInitialized => _isInitialized;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Check AR support first
      _isARSupported = await _checkARSupport();

      if (!_isARSupported) {
        throw Exception('AR is not supported on this device');
      }

      // Initialize camera
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('No cameras available on this device');
      }

      _cameraController = CameraController(
        cameras.first,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _cameraController!.initialize();

      _isInitialized = true;
      debugPrint('AR Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing AR service: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  /// Check if AR is supported on this device
  Future<bool> _checkARSupport() async {
    try {
      // For Android, check ARCore availability
      if (Platform.isAndroid) {
        // This would typically check ARCore availability
        // For now, we'll assume it's available if the plugin is present
        return true;
      }

      // For iOS, check ARKit availability
      if (Platform.isIOS) {
        // This would typically check ARKit availability
        // For now, we'll assume it's available on iOS 11+
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error checking AR support: $e');
      return false;
    }
  }

  Future<void> startAR() async {
    if (!_isInitialized) {
      await initialize();
    }

    // Additional AR initialization can be done here
  }

  void stopAR() {
    _cameraController?.dispose();
    _arController?.dispose();
    _isInitialized = false;
  }

  Future<Experience?> detectExperienceInView() async {
    // This is a mock implementation
    // In a real app, this would use computer vision or location-based detection

    // Randomly return an experience or null
    if (DateTime.now().millisecondsSinceEpoch % 5 == 0) {
      return Experience(
        id: 'exp1',
        title: 'Cultural Tour of Lagos',
        description:
            'Explore the rich cultural heritage of Lagos with our expert guides.',
        category: 'Cultural',
        price: 50.0,
        location: 'Lagos, Nigeria',
        imageUrl: 'https://example.com/lagos.jpg',
        rating: 4.5,
        reviewCount: 120,
        guideId: 'guide1',
        guideName: 'John Doe',
        guideImageUrl: 'https://example.com/guide.jpg',
        coordinates: const LatLng(6.5244, 3.3792),
        languages: ['English', 'Yoruba'],
        includedItems: ['Tour guide', 'Water', 'Snacks'],
        requirements: ['Comfortable shoes', 'Sun protection'],
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
        durationHours: 2.0,
      );
    }

    return null;
  }

  void onArCoreViewCreated(ArCoreController controller) {
    _arController = controller;
    _setupARScene();
  }

  void _setupARScene() {
    if (_arController == null) return;

    _arController!.onNodeTap = (nodeNames) {
      debugPrint('Node tapped: $nodeNames');
    };
    _arController!.onPlaneTap = (hits) {
      debugPrint('Plane tapped: ${hits.length} hits');
    };
  }

  Future<void> addNode({
    required Landmark landmark,
    required Vector3 position,
    required Vector3 scale,
    required Vector3 rotation,
  }) async {
    if (_arController == null) return;

    final node = ArCoreNode(
      name: landmark.id,
      shape: ArCoreSphere(
        materials: [
          ArCoreMaterial(
            color: const Color.fromARGB(255, 66, 103, 178),
            metallic: 1.0,
            roughness: 0.5,
          ),
        ],
        radius: 0.1,
      ),
      position: position,
      scale: Vector3(scale.x, scale.y, scale.z),
      rotation: Vector4(rotation.x, rotation.y, rotation.z, 1.0),
    );

    _arController!.addArCoreNode(node);
  }

  void removeNode(String nodeName) {
    if (_arController == null) return;
    _arController!.removeNode(nodeName: nodeName);
  }

  void dispose() {
    _cameraController?.dispose();
    _arController?.dispose();
    _isInitialized = false;
  }
}
