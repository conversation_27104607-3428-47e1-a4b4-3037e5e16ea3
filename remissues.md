flutter: 🐛 DEBUG [2025-07-20T03:35:59.578324] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:35:59.744846] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-20T03:35:59.911560] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:00.494657] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:00.694953] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-20T03:36:01.028322] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-20T03:36:01.161883] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:02.844822] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:03.411344] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:03.678061] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:04.028456] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:04.311572] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:04.595056] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:05.028273] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:06.161295] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:06.229031] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:06.344883] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:06.511611] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:06.802979] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-20T03:36:06.844689] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-20T03:36:06.994718] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:08.211448] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-20T03:36:08.511415] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:08.744739] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:09.461450] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-20T03:36:09.583016] [PerformanceMonitoringService] High memory usage detected {"memory_mb":182.0}
flutter: 🐛 DEBUG [2025-07-20T03:36:09.628086] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:10.361573] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:10.561561] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:10.728407] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:14.029084] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:14.128399] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:15.194796] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: 🐛 DEBUG [2025-07-20T03:36:16.285441] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-20T03:36:16.347376] [PerformanceMonitoringService] Slow frame detected {"duration_ms":61}
flutter: 🐛 DEBUG [2025-07-20T03:36:16.462498] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: Unexpected error during login: type 'List<Object?>' is not a subtype of type 'PigeonUserDetails?' in type cast
flutter: AutoLockService initialized with settings
flutter: Offline mode: Loading bookings from cache
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-20T03:36:17.884415] [EnhancedOfflineModeService] Loaded offline settings
flutter: 🐛 DEBUG [2025-07-20T03:36:17.886759] [EnhancedOfflineModeService] Loaded 0 offline content items
flutter: 🐛 DEBUG [2025-07-20T03:36:17.888975] [EnhancedOfflineModeService] Loaded 0 content conflicts
flutter: 🐛 DEBUG [2025-07-20T03:36:17.891269] [EnhancedOfflineModeService] Loaded 0 bandwidth usage records
flutter: 🐛 DEBUG [2025-07-20T03:36:17.901010] [EnhancedOfflineModeService] Loaded 1 sync schedules
flutter: 🐛 DEBUG [2025-07-20T03:36:17.930722] [PerformanceMonitoringService] Slow frame detected {"duration_ms":794}
flutter: ❌ ERROR [2025-07-20T03:36:18.143299] [FlutterError] A RenderFlex overflowed by 10.0 pixels on the bottom. A RenderFlex overflowed by 10.0 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #31     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #32     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #33     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #34     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #35     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #36     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #37     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #38     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #39     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #40     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #44     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #45     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #46     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #47     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #48     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #49     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #50     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #64     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #65     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #71     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #73     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #74     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #81     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #85     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #86     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #87     _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #108    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #112    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #113    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #120    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #129    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #130    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #131    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #133    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #134    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #135    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #136    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #137    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #140    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #141    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #144    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #145    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #146    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #147    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #148    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #149    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #150    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #151    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #152    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #156    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #157    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #158    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #159    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #160    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #161    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #162    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #163    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #164    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #165    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #166    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #170    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #171    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #172    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #173    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #174    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #175    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #176    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #177    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #178    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #181    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #182    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #183    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #184    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #185    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #186    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #187    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #188    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #189    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #190    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #191    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #192    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #193    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #194    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #195    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #196    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #197    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #202    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #203    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #204    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #205    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #206    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #207    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #208    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #209    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #210    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #211    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #212    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #213    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #214    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #215    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #216    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #217    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #218    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #219    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #220    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #221    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #222    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #223    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #224    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #225    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #226    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #227    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #228    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #229    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #230    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #231    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #232    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #233    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #234    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #235    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #236    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #237    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #243    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #244    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #245    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #246    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #247    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #248    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #249    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #250    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #251    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #252    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #253    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #254    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #255    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #256    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #257    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #258    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #259    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #260    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #261    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #262    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #263    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #264    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #265    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #266    _invoke (dart:ui/hooks.dart:312:13)
flutter: #267    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #268    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-20T03:36:18.190026] [FlutterError] A RenderFlex overflowed by 10.0 pixels on the bottom. A RenderFlex overflowed by 10.0 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #31     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #32     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #33     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #34     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #35     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #36     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #37     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #38     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #39     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #40     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #44     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #45     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #46     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #47     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #48     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #49     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #50     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #64     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #65     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #71     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #73     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #74     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #81     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #85     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #86     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #87     _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #108    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #112    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #113    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #120    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #129    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #130    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #131    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #133    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #134    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #135    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #136    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #137    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #140    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #141    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #144    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #145    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #146    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #147    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #148    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #149    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #150    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #151    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #152    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #156    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #157    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #158    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #159    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #160    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #161    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #162    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #163    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #164    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #165    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #166    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #170    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #171    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #172    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #173    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #174    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #175    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #176    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #177    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #178    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #181    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #182    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #183    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #184    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #185    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #186    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #187    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #188    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #189    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #190    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #191    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #192    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #193    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #194    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #195    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #196    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #197    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #202    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #203    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #204    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #205    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #206    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #207    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #208    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #209    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #210    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #211    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #212    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #213    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #214    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #215    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #216    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #217    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #218    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #219    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #220    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #221    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #222    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #223    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #224    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #225    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #226    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #227    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #228    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #229    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #230    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #231    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #232    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #233    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #234    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #235    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #236    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #237    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #243    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #244    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #245    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #246    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #247    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #248    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #249    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #250    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #251    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #252    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #253    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #254    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #255    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #256    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #257    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #258    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #259    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #260    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #261    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #262    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #263    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #264    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #265    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #266    _invoke (dart:ui/hooks.dart:312:13)
flutter: #267    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #268    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-20T03:36:18.192730] [FlutterError] A RenderFlex overflowed by 10.0 pixels on the bottom. A RenderFlex overflowed by 10.0 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #31     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #32     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #33     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #34     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #35     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #36     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #37     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #38     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #39     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #40     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #44     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #45     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #46     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #47     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #48     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #49     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #50     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #64     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #65     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #71     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #73     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #74     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #77     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #78     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #80     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #81     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #82     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #83     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #84     _RenderSingleChildViewport.paint.paintContents (package:flutter/src/widgets/single_child_scroll_view.dart:538:17)
flutter: #85     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #86     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #87     _RenderSingleChildViewport.paint (package:flutter/src/widgets/single_child_scroll_view.dart:542:40)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #98     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #99     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #102    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #103    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #105    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #106    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #107    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #108    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #112    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #113    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #120    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #129    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #130    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #131    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #133    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #134    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #135    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #136    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #137    _RenderVisibility.paint (package:flutter/src/widgets/visibility.dart:601:11)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #140    RenderIndexedStack.paintStack (package:flutter/src/rendering/stack.dart:813:13)
flutter: #141    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #144    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #145    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #146    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #147    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #148    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #149    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #150    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #151    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #152    RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #153    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #154    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #155    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #156    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #157    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #158    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #159    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #160    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #161    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #162    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #163    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #164    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #165    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #166    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #170    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #171    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #172    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #173    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #174    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #175    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #176    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #177    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #178    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #179    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #180    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #181    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #182    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #183    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #184    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #185    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #186    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #187    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #188    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #189    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #190    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #191    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #192    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #193    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #194    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #195    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #196    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #197    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #202    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #203    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #204    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #205    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #206    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #207    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #208    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #209    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #210    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #211    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #212    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #213    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #214    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #215    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #216    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #217    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #218    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #219    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #220    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #221    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #222    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #223    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #224    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #225    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #226    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #227    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #228    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #229    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #230    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #231    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #232    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #233    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #234    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #235    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #236    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #237    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #238    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #239    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #240    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #241    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #242    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #243    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #244    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #245    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #246    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #247    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #248    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #249    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #250    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #251    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #252    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #253    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #254    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #255    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #256    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #257    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #258    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #259    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #260    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #261    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #262    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #263    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #264    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #265    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #266    _invoke (dart:ui/hooks.dart:312:13)
flutter: #267    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #268    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-20T03:36:18.253775] [EnhancedOfflineModeService] Starting offline content sync
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: ❌ ERROR [2025-07-20T03:36:18.286263] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-20T03:36:18.288467] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-20T03:36:18.290218] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-20T03:36:18.290636] [OfflineModeService] Offline content sync completed
flutter: 🐛 DEBUG [2025-07-20T03:36:18.290902] [PerformanceMonitoringService] Slow frame detected {"duration_ms":362}
flutter: 🐛 DEBUG [2025-07-20T03:36:18.311551] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-20T03:36:18.600872] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-20T03:36:18.628273] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-20T03:36:18.695689] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:18.784265] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-20T03:36:19.240076] [PerformanceMonitoringService] Slow frame detected {"duration_ms":110}
flutter: 🐛 DEBUG [2025-07-20T03:36:19.266200] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-20T03:36:19.361777] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:19.412828] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:19.495022] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-20T03:36:19.528359] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: 🐛 DEBUG [2025-07-20T03:36:20.296915] [PerformanceMonitoringService] Slow frame detected {"duration_ms":132}
flutter: 🐛 DEBUG [2025-07-20T03:36:20.362602] [PerformanceMonitoringService] Slow frame detected {"duration_ms":67}
flutter: 🐛 DEBUG [2025-07-20T03:36:20.478385] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-20T03:36:20.512131] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:20.561976] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:20.861669] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:21.011616] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:21.061627] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:21.111675] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:21.195093] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-20T03:36:21.374635] [PerformanceMonitoringService] Slow frame detected {"duration_ms":94}
flutter: 🐛 DEBUG [2025-07-20T03:36:21.395034] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-20T03:36:21.444943] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:21.978374] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:22.078478] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:22.211708] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:22.458476] [PerformanceMonitoringService] Slow frame detected {"duration_ms":94}
flutter: 🐛 DEBUG [2025-07-20T03:36:22.529041] [PerformanceMonitoringService] Slow frame detected {"duration_ms":72}
flutter: 🐛 DEBUG [2025-07-20T03:36:22.561674] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: Warning: User model is null, user may need to re-authenticate
flutter: 🐛 DEBUG [2025-07-20T03:36:22.845926] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: Profile screen user data: null
flutter: Profile screen: User model is null
flutter: 🐛 DEBUG [2025-07-20T03:36:22.978322] [PerformanceMonitoringService] Slow frame detected {"duration_ms":132}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.012022] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.061940] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.111644] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.144949] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.194950] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.278313] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.312297] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.361674] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.407729] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.445083] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.534279] [PerformanceMonitoringService] Slow frame detected {"duration_ms":87}
flutter: 🐛 DEBUG [2025-07-20T03:36:23.561737] [PerformanceMonitoringService] Slow frame detected {"duration_ms":28}
flutter: 🐛 DEBUG [2025-07-20T03:36:24.575889] [PerformanceMonitoringService] Slow frame detected {"duration_ms":47}
flutter: ⚠️ WARNING [2025-07-20T03:36:24.583224] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
flutter: 🐛 DEBUG [2025-07-20T03:36:24.595180] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18}
flutter: ❌ ERROR [2025-07-20T03:36:27.049018] [FlutterError] A RenderFlex overflowed by 10.0 pixels on the bottom. A RenderFlex overflowed by 10.0 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #31     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #32     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #33     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #34     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #35     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #36     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #37     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #38     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #39     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #40     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #44     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #45     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #46     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #47     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #48     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #49     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #50     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #51     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #52     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #53     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #54     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #55     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #56     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #57     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #58     _invoke (dart:ui/hooks.dart:312:13)
flutter: #59     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #60     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-20T03:36:27.095203] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-20T03:36:33.745346] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-20T03:36:39.583628] [PerformanceMonitoringService] High memory usage detected {"memory_mb":183.0}
